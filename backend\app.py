import os
from flask import Flask, render_template, request, jsonify
from werkzeug.utils import secure_filename
from PIL import Image
import pytesseract

app = Flask(__name__)

# Configure upload folder
UPLOAD_FOLDER = 'uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Allowed file extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    print("Index route accessed")
    try:
        result = render_template('index.html')
        print(f"Template rendered successfully, length: {len(result)}")
        return result
    except Exception as e:
        print(f"Error rendering template: {e}")
        return f"Error: {e}"

@app.route('/upload', methods=['POST'])
def upload_file():
    # Check if file was submitted
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
        
    file = request.files['file']
    
    # Check if file is selected
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
        
    # Validate file type
    if not allowed_file(file.filename):
        return jsonify({'error': 'Invalid file type. Allowed: PNG, JPG, JPEG'}), 400
    
    try:
        # Secure filename and save temporarily
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Process image with OCR
        img = Image.open(filepath)
        text = pytesseract.image_to_string(img)
        
        # Clean up temporary file
        os.remove(filepath)
        
        return jsonify({'text': text})
        
    except Exception as e:
        # Clean up if file was saved
        if os.path.exists(filepath):
            os.remove(filepath)
        return jsonify({'error': f'OCR processing failed: {str(e)}'}), 500

if __name__ == '__main__':
    print("Starting Flask application...")
    app.run(debug=True, host='127.0.0.1', port=5000)