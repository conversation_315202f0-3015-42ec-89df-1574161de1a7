<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image to Text OCR</title>
    <style>
        :root {
            --primary: #3498db;
            --primary-dark: #2980b9;
            --secondary: #2c3e50;
            --light: #ecf0f1;
            --danger: #e74c3c;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            padding: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        h1 {
            color: var(--secondary);
            margin-bottom: 10px;
            text-align: center;
        }

        p.subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            text-align: center;
        }

        .upload-area {
            margin-bottom: 20px;
            border: 2px dashed #bdc3c7;
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        .upload-area:hover {
            border-color: var(--primary);
            background-color: #e8f4ff;
        }

        .file-label {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            color: #7f8c8d;
            font-weight: 500;
        }

        .file-label svg {
            margin-bottom: 10px;
            color: var(--primary);
        }

        #file-input {
            display: none;
        }

        .btn {
            display: block;
            width: 100%;
            padding: 14px;
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
            box-shadow: var(--shadow);
        }

        .btn:hover {
            background-color: var(--primary-dark);
        }

        .btn:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .loader {
            display: none;
            text-align: center;
            padding: 20px;
            color: var(--primary);
            font-weight: 500;
        }

        .result-container {
            margin-top: 30px;
            display: none;
        }

        .result-container h2 {
            color: var(--secondary);
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        pre {
            background-color: #f8f9fa;
            border: 1px solid #e1e4e8;
            border-radius: var(--border-radius);
            padding: 20px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-family: inherit;
            line-height: 1.5;
        }

        .error {
            color: var(--danger);
            margin-top: 10px;
            text-align: center;
            display: none;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }
            
            .upload-area {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Image to Text Converter</h1>
        <p class="subtitle">Upload an image to extract text using OCR</p>
        
        <form id="ocr-form">
            <div class="upload-area">
                <input type="file" id="file-input" accept=".png,.jpg,.jpeg" required>
                <label for="file-input" class="file-label">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="17 8 12 3 7 8"></polyline>
                        <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                    <span id="file-name">Choose an image (PNG, JPG)</span>
                </label>
            </div>
            <div id="form-error" class="error"></div>
            <button type="submit" class="btn" id="submit-btn">Extract Text</button>
        </form>
        
        <div class="loader" id="loader">Processing... Please wait</div>
        
        <div class="result-container" id="result-container">
            <h2>Extracted Text:</h2>
            <pre id="result-text"></pre>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const form = document.getElementById('ocr-form');
            const fileInput = document.getElementById('file-input');
            const fileName = document.getElementById('file-name');
            const resultText = document.getElementById('result-text');
            const loader = document.getElementById('loader');
            const resultContainer = document.getElementById('result-container');
            const formError = document.getElementById('form-error');
            const submitBtn = document.getElementById('submit-btn');
            
            // Update file name display
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    fileName.textContent = e.target.files[0].name;
                    formError.style.display = 'none';
                }
            });
            
            // Handle form submission
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                formError.style.display = 'none';
                resultContainer.style.display = 'none';
                
                // Validate file selection
                if (!fileInput.files || fileInput.files.length === 0) {
                    showError('Please select a file first');
                    return;
                }
                
                const file = fileInput.files[0];
                const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
                
                // Validate file type
                if (!validTypes.includes(file.type)) {
                    showError('Invalid file type. Please upload a JPG, JPEG, or PNG image');
                    return;
                }
                
                // Validate file size (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    showError('File size too large. Maximum 5MB allowed');
                    return;
                }
                
                // Show loader, disable button
                loader.style.display = 'block';
                submitBtn.disabled = true;
                
                try {
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    // Send to backend
                    const response = await fetch('/upload', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (!response.ok) {
                        throw new Error(data.error || 'OCR processing failed');
                    }
                    
                    // Display results
                    resultText.textContent = data.text || 'No text could be extracted';
                    resultText.style.color = '#333';
                    resultContainer.style.display = 'block';
                } catch (error) {
                    showError(error.message);
                } finally {
                    loader.style.display = 'none';
                    submitBtn.disabled = false;
                }
            });
            
            function showError(message) {
                formError.textContent = message;
                formError.style.display = 'block';
            }
        });
    </script>
</body>
</html>